const nodemailer = require("nodemailer");
const fs = require("fs");
const archiver = require("archiver");

async function zipReport() {
  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream("report.zip");
    const archive = archiver("zip");

    output.on("close", () => {
      console.log(`Zipped ${archive.pointer()} total bytes`);
      resolve();
    });

    archive.on("error", (err) => reject(err));

    archive.pipe(output);
    archive.directory("playwright-report/", false);
    archive.finalize();
  });
}

async function sendEmail() {
  // Zip the report first
  await zipReport();

  // Create transporter with your SMTP details
  let transporter = nodemailer.createTransport({
    host: "smtp.gmail.com",
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: "<EMAIL>",
      pass: "miaujqebcgmdyzlg",
    },
  });

  // Send mail with attachment
  let info = await transporter.sendMail({
    from: '"Playwright Tests" <<EMAIL>>',
    to: "<EMAIL>",
    subject: "Playwright Test Report",
    text: "Please find the attached Playwright test report.",
    attachments: [
      {
        filename: "report.zip",
        path: "./report.zip",
      },
    ],
  });

  console.log("Message sent: %s", info.messageId);
}

sendEmail().catch(console.error);
