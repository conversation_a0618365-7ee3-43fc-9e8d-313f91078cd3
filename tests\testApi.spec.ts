import { test, expect, request } from '@playwright/test';

//
// Product Admin Login - Success
//
test('Product Admin login success', async ({ page }) => {
  await page.goto('https://talkio.invictainnovations.com/login');
  await page.fill('#username', 'admin');
  await page.fill('#password', 'admin123');
  await page.click('button[type="submit"]');

  await expect(page).toHaveURL(/organization);
  await expect(page.locator('h1')).toContainText('Welcome');
});

//
// Product Admin Login - Wrong Password
//
test('Product Admin login fails with wrong password', async ({ page }) => {
  await page.goto('https://talkio.invictainnovations.com/login');
  await page.fill('#username', 'productAdminUser');
  await page.fill('#password', 'wrongPassword123');
  await page.click('button[type="submit"]');

  await expect(page.locator('.error-message')).toHaveText(/invalid/i);
});

//
// Org Admin Login - Success
//
test('Org Admin login success', async ({ page }) => {
  await page.goto('https://talkio.invictainnovations.com/login');
  await page.fill('#username', '<EMAIL>');
  await page.fill('#password', 'OrgAdminPass123');
  await page.click('button[type="submit"]');

  await expect(page).toHaveURL(/organization/);
  await expect(page.locator('h1')).toContainText('Welcome');
});

//
// Org Admin Login - Wrong Password
//
test('Org Admin login fails with wrong password', async ({ page }) => {
  await page.goto('https://talkio.invictainnovations.com/login');
  await page.fill('#username', 'admin');
  await page.fill('#password', 'wrongPass');
  await page.click('button[type="submit"]');

  await expect(page.locator('.error-message')).toHaveText(/invalid/i);
});
