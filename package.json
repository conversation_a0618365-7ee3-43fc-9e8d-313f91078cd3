{"$schema": "http://json.schemastore.org/package", "name": "play", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test:report": "npx playwright test --reporter=html", "send-report": "node sendReportEmail.js", "test-and-email": "npm run test:report && npm run send-report"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.54.2", "@types/node": "^24.2.1"}, "dependencies": {"archiver": "^7.0.1", "nodemailer": "^7.0.5"}}